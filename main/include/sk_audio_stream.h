/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_audio_stream.h
 * @description: WebSocket音频流处理模块头文件
 * @author: <PERSON>
 * @date: 2025-07-28
 */

#ifndef __SK_AUDIO_STREAM_H__
#define __SK_AUDIO_STREAM_H__

#include <stdint.h>
#include <stdbool.h>
#include "sk_common.h"

#ifdef __cplusplus
extern "C" {
#endif

// 音频流格式定义
#define SK_AUDIO_STREAM_VERSION         0x01

// 音频格式类型
enum {
    SK_AUDIO_FORMAT_PCM = 0x01,
    SK_AUDIO_FORMAT_OPUS = 0x02,
};

// 音频流标志位
enum {
    SK_AUDIO_FLAG_START = 0x01,     // 流开始
    SK_AUDIO_FLAG_END = 0x02,       // 流结束
    SK_AUDIO_FLAG_MUTE = 0x04,      // 静音帧
    SK_AUDIO_FLAG_SYNC = 0x08,      // 同步帧
};

// 音频流状态
enum {
    SK_AUDIO_STREAM_IDLE = 0,
    SK_AUDIO_STREAM_PLAYING,
    SK_AUDIO_STREAM_PAUSED,
    SK_AUDIO_STREAM_ERROR,
};

// WebSocket音频帧格式
typedef struct {
    uint8_t version;        // 协议版本
    uint8_t type;           // 数据类型 (SK_WS_DATA_TYPE_AUDIO)
    uint16_t seqNum;        // 序列号
    uint16_t payloadLen;    // 音频数据长度
    uint8_t audioFormat;    // 音频格式
    uint8_t flags;          // 标志位
    uint8_t audioData[0];   // 音频数据
} __attribute__((packed)) SkWsAudioFrame_t;

// 音频流统计信息
typedef struct {
    uint32_t totalFrames;       // 总帧数
    uint32_t lostFrames;        // 丢失帧数
    uint32_t duplicateFrames;   // 重复帧数
    uint32_t totalBytes;        // 总字节数
    uint32_t playTime;          // 播放时间(ms)
} SkAudioStreamStats_t;

// 音频流控制结构
typedef struct {
    uint8_t state;              // 当前状态
    uint16_t expectedSeq;       // 期望的序列号
    uint32_t sessionId;         // 会话ID
    bool isPlaying;             // 是否正在播放
    SkAudioStreamStats_t stats; // 统计信息
    void *opusDecHandler;       // Opus解码器句柄
} SkAudioStreamCtrl_t;

// 音频流事件回调
typedef void (*SkAudioStreamEventCallback_t)(uint8_t event, uint32_t param);

// 音频流事件类型
enum {
    SK_AUDIO_STREAM_EVENT_START = 1,
    SK_AUDIO_STREAM_EVENT_END,
    SK_AUDIO_STREAM_EVENT_ERROR,
    SK_AUDIO_STREAM_EVENT_STATS,
};

/**
 * @brief 初始化音频流处理模块
 * @return int32_t 初始化结果
 */
int32_t SkAudioStreamInit(void);

/**
 * @brief 反初始化音频流处理模块
 */
void SkAudioStreamDeinit(void);

/**
 * @brief 处理WebSocket接收到的音频数据
 * @param data 音频数据指针
 * @param len 数据长度
 * @return int32_t 处理结果
 */
int32_t SkAudioStreamProcessData(void *data, uint16_t len);

/**
 * @brief 开始音频流播放
 * @param sessionId 会话ID
 * @return int32_t 操作结果
 */
int32_t SkAudioStreamStart(uint32_t sessionId);

/**
 * @brief 停止音频流播放
 * @return int32_t 操作结果
 */
int32_t SkAudioStreamStop(void);

/**
 * @brief 暂停音频流播放
 * @return int32_t 操作结果
 */
int32_t SkAudioStreamPause(void);

/**
 * @brief 恢复音频流播放
 * @return int32_t 操作结果
 */
int32_t SkAudioStreamResume(void);

/**
 * @brief 获取音频流状态
 * @return uint8_t 当前状态
 */
uint8_t SkAudioStreamGetState(void);

/**
 * @brief 获取音频流统计信息
 * @param stats 统计信息结构指针
 * @return int32_t 操作结果
 */
int32_t SkAudioStreamGetStats(SkAudioStreamStats_t *stats);

/**
 * @brief 注册音频流事件回调
 * @param callback 回调函数
 */
void SkAudioStreamRegEventCallback(SkAudioStreamEventCallback_t callback);

/**
 * @brief 重置音频流统计信息
 */
void SkAudioStreamResetStats(void);

#ifdef __cplusplus
}
#endif

#endif /* __SK_AUDIO_STREAM_H__ */
