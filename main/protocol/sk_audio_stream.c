/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_audio_stream.c
 * @description: WebSocket音频流处理模块实现
 * @author: <PERSON>
 * @date: 2025-07-28
 */

#include <string.h>
#include <stdlib.h>
#include "sk_log.h"
#include "sk_os.h"
#include "sk_audio_stream.h"
#include "sk_websocket.h"
#include "sk_opus_dec.h"
#include "sk_audio_buffer.h"

static const char *TAG = "SkAudioStream";

// 全局音频流控制结构
static SkAudioStreamCtrl_t g_audioStreamCtrl;
static SkAudioStreamEventCallback_t g_eventCallback = NULL;

// 内部函数声明
static int32_t ProcessOpusFrame(SkWsAudioFrame_t *frame);
static int32_t ProcessPcmFrame(SkWsAudioFrame_t *frame);
static void UpdateStats(SkWsAudioFrame_t *frame);
static void TriggerEvent(uint8_t event, uint32_t param);

int32_t SkAudioStreamInit(void) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;

    SK_LOGI(TAG, "Initializing audio stream module");

    // 初始化控制结构
    memset(ctrl, 0, sizeof(SkAudioStreamCtrl_t));
    ctrl->state = SK_AUDIO_STREAM_IDLE;
    ctrl->expectedSeq = 0;
    ctrl->isPlaying = false;

    // 获取Opus解码器句柄
    ctrl->opusDecHandler = SkOpusDecGetHandler();
    if (ctrl->opusDecHandler == NULL) {
        SK_LOGE(TAG, "Failed to get Opus decoder handler");
        return SK_RET_FAIL;
    }

    // 启用Opus解码器的远程播放功能
    SkOpusDecEnableRemote(1);
    SK_LOGI(TAG, "Enabled Opus decoder remote playback");

    SK_LOGI(TAG, "Audio stream module initialized successfully");
    return SK_RET_SUCCESS;
}

void SkAudioStreamDeinit(void) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    SK_LOGI(TAG, "Deinitializing audio stream module");
    
    // 停止播放
    SkAudioStreamStop();
    
    // 清理资源
    memset(ctrl, 0, sizeof(SkAudioStreamCtrl_t));
    g_eventCallback = NULL;
    
    SK_LOGI(TAG, "Audio stream module deinitialized");
}

int32_t SkAudioStreamProcessData(void *data, uint16_t len) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    SkWsAudioFrame_t *frame = (SkWsAudioFrame_t *)data;

    // 验证数据长度
    if (len < sizeof(SkWsAudioFrame_t)) {
        SK_LOGD(TAG, "Frame too small: %d", len);
        return SK_RET_FAIL;
    }

    // 验证协议版本 - 如果不匹配，静默返回，不是音频数据
    if (frame->version != SK_AUDIO_STREAM_VERSION) {
        SK_LOGD(TAG, "Not audio stream version: 0x%02x", frame->version);
        return SK_RET_FAIL;
    }

    // 验证数据类型 - 如果不匹配，静默返回，不是音频数据
    if (frame->type != SK_WS_DATA_TYPE_AUDIO) {
        SK_LOGD(TAG, "Not audio data type: 0x%02x", frame->type);
        return SK_RET_FAIL;
    }
    
    // 验证载荷长度
    if (frame->payloadLen != (len - sizeof(SkWsAudioFrame_t))) {
        SK_LOGE(TAG, "Payload length mismatch: expected %d, got %d", 
                frame->payloadLen, len - sizeof(SkWsAudioFrame_t));
        return SK_RET_FAIL;
    }
    
    SK_LOGD(TAG, "Processing audio frame: seq=%d, format=%d, flags=0x%02x, len=%d",
            frame->seqNum, frame->audioFormat, frame->flags, frame->payloadLen);
    
    // 处理流开始标志
    if (frame->flags & SK_AUDIO_FLAG_START) {
        SK_LOGI(TAG, "Audio stream started");
        ctrl->state = SK_AUDIO_STREAM_PLAYING;
        ctrl->expectedSeq = frame->seqNum;
        ctrl->isPlaying = true;
        TriggerEvent(SK_AUDIO_STREAM_EVENT_START, frame->seqNum);
    }
    
    // 检查序列号
    if (ctrl->isPlaying) {
        if (frame->seqNum != ctrl->expectedSeq) {
            if (frame->seqNum < ctrl->expectedSeq) {
                // 重复帧
                ctrl->stats.duplicateFrames++;
                SK_LOGW(TAG, "Duplicate frame: seq=%d, expected=%d", 
                        frame->seqNum, ctrl->expectedSeq);
                return SK_RET_SUCCESS;
            } else {
                // 丢失帧
                uint16_t lostCount = frame->seqNum - ctrl->expectedSeq;
                ctrl->stats.lostFrames += lostCount;
                SK_LOGW(TAG, "Lost %d frames: seq=%d, expected=%d", 
                        lostCount, frame->seqNum, ctrl->expectedSeq);
            }
        }
        ctrl->expectedSeq = frame->seqNum + 1;
    }
    
    // 更新统计信息
    UpdateStats(frame);
    
    // 处理流结束标志
    if (frame->flags & SK_AUDIO_FLAG_END) {
        SK_LOGI(TAG, "Audio stream ended");
        ctrl->state = SK_AUDIO_STREAM_IDLE;
        ctrl->isPlaying = false;
        TriggerEvent(SK_AUDIO_STREAM_EVENT_END, frame->seqNum);
        return SK_RET_SUCCESS;
    }
    
    // 如果不在播放状态，忽略音频数据
    if (!ctrl->isPlaying) {
        return SK_RET_SUCCESS;
    }
    
    // 处理静音帧
    if (frame->flags & SK_AUDIO_FLAG_MUTE) {
        SK_LOGD(TAG, "Mute frame received");
        return SK_RET_SUCCESS;
    }
    
    // 根据音频格式处理数据
    switch (frame->audioFormat) {
        case SK_AUDIO_FORMAT_OPUS:
            return ProcessOpusFrame(frame);
        case SK_AUDIO_FORMAT_PCM:
            return ProcessPcmFrame(frame);
        default:
            SK_LOGE(TAG, "Unsupported audio format: %d", frame->audioFormat);
            return SK_RET_FAIL;
    }
}

static int32_t ProcessOpusFrame(SkWsAudioFrame_t *frame) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    SkAudioDownlinkTimeRecord timeRecord;

    // 填充时间记录
    memset(&timeRecord, 0, sizeof(SkAudioDownlinkTimeRecord));
    timeRecord.decRxTick = SkOsGetTickCnt();
    
    // 调用Opus解码器处理音频数据
    int32_t result = SkOpusDecPlayRemote(ctrl->opusDecHandler, 
                                        ctrl->sessionId,
                                        frame->audioData, 
                                        frame->payloadLen, 
                                        &timeRecord);
    
    if (result != SK_RET_SUCCESS) {
        SK_LOGE(TAG, "Failed to process Opus frame");
        ctrl->state = SK_AUDIO_STREAM_ERROR;
        TriggerEvent(SK_AUDIO_STREAM_EVENT_ERROR, result);
        return SK_RET_FAIL;
    }
    
    SK_LOGD(TAG, "Opus frame processed successfully");
    return SK_RET_SUCCESS;
}

static int32_t ProcessPcmFrame(SkWsAudioFrame_t *frame) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    uint8_t *processData = NULL;
    int32_t processLen = 0;

    SK_LOGD(TAG, "Processing PCM frame: %d bytes", frame->payloadLen);

    // 为PCM数据添加4字节头部，以符合SkOpusDecPlayRemote的期望格式
    processLen = frame->payloadLen + 4;
    processData = malloc(processLen);
    if (processData == NULL) {
        SK_LOGE(TAG, "Failed to allocate memory for PCM processing");
        return SK_RET_FAIL;
    }

    // 添加4字节头部 (可以是任意值，因为PCM不需要解码)
    processData[0] = 0x00;
    processData[1] = 0x00;
    processData[2] = 0x00;
    processData[3] = 0x00;

    // 复制PCM数据
    memcpy(processData + 4, frame->audioData, frame->payloadLen);

    // 填充时间记录
    SkAudioDownlinkTimeRecord timeRecord;
    memset(&timeRecord, 0, sizeof(SkAudioDownlinkTimeRecord));
    timeRecord.decRxTick = SkOsGetTickCnt();

    // 使用Opus解码器的远程播放接口
    int32_t result = SkOpusDecPlayRemote(ctrl->opusDecHandler,
                                        ctrl->sessionId,
                                        processData,
                                        processLen,
                                        &timeRecord);

    free(processData);

    if (result != SK_RET_SUCCESS) {
        SK_LOGE(TAG, "Failed to play PCM frame: %d", result);
        return SK_RET_FAIL;
    }

    SK_LOGD(TAG, "PCM frame processed successfully");
    return SK_RET_SUCCESS;
}

static void UpdateStats(SkWsAudioFrame_t *frame) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    ctrl->stats.totalFrames++;
    ctrl->stats.totalBytes += frame->payloadLen;
    
    // 计算播放时间 (假设每帧60ms)
    ctrl->stats.playTime = ctrl->stats.totalFrames * 60;
}

static void TriggerEvent(uint8_t event, uint32_t param) {
    if (g_eventCallback != NULL) {
        g_eventCallback(event, param);
    }
}

int32_t SkAudioStreamStart(uint32_t sessionId) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    SK_LOGI(TAG, "Starting audio stream with session ID: %u", sessionId);
    
    ctrl->sessionId = sessionId;
    ctrl->state = SK_AUDIO_STREAM_PLAYING;
    ctrl->expectedSeq = 0;
    ctrl->isPlaying = true;
    
    // 重置统计信息
    SkAudioStreamResetStats();
    
    return SK_RET_SUCCESS;
}

int32_t SkAudioStreamStop(void) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    SK_LOGI(TAG, "Stopping audio stream");
    
    ctrl->state = SK_AUDIO_STREAM_IDLE;
    ctrl->isPlaying = false;
    
    // 停止Opus解码器
    if (ctrl->opusDecHandler != NULL) {
        SkOpusDecRemoteDataEnd(ctrl->opusDecHandler, ctrl->sessionId);
    }
    
    return SK_RET_SUCCESS;
}

int32_t SkAudioStreamPause(void) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->state == SK_AUDIO_STREAM_PLAYING) {
        ctrl->state = SK_AUDIO_STREAM_PAUSED;
        SK_LOGI(TAG, "Audio stream paused");
    }
    
    return SK_RET_SUCCESS;
}

int32_t SkAudioStreamResume(void) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    
    if (ctrl->state == SK_AUDIO_STREAM_PAUSED) {
        ctrl->state = SK_AUDIO_STREAM_PLAYING;
        SK_LOGI(TAG, "Audio stream resumed");
    }
    
    return SK_RET_SUCCESS;
}

uint8_t SkAudioStreamGetState(void) {
    return g_audioStreamCtrl.state;
}

int32_t SkAudioStreamGetStats(SkAudioStreamStats_t *stats) {
    if (stats == NULL) {
        return SK_RET_FAIL;
    }
    
    memcpy(stats, &g_audioStreamCtrl.stats, sizeof(SkAudioStreamStats_t));
    return SK_RET_SUCCESS;
}

void SkAudioStreamRegEventCallback(SkAudioStreamEventCallback_t callback) {
    g_eventCallback = callback;
}

void SkAudioStreamResetStats(void) {
    memset(&g_audioStreamCtrl.stats, 0, sizeof(SkAudioStreamStats_t));
    SK_LOGI(TAG, "Audio stream statistics reset");
}
