/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: testcase_websocket.c
 * @description: websocekt测试用例文件
 * @author: <PERSON>
 * @date: 2025-07-18
 */
#include <freertos/FreeRTOS.h>
#include "sk_log.h"
#include "sk_config.h"
#include "sk_wifi.h"
#include "sk_websocket.h"
#include "sk_test_common.h"
#include "sk_audio_stream.h"
#include "sk_opus.h"
#include "sk_audio.h"
#include "sk_opus_dec.h"
#include "sk_board.h"

#define SK_WS_TS_BUF_SIZE 2048

bool g_skTcWifiState = false;
bool g_skTcWsConnected = false;
bool g_skTcWsReconnected = false;
bool g_skTcWsDisconnect = false;
bool g_skTcWsBinDataRecv = false;
bool g_skTcWsTxtDataRecv = false;
uint8_t *g_wsTsTxBuf = NULL;
uint8_t *g_wsTsSrcBuf = NULL;
uint8_t *g_wsTsRxBuf = NULL;
size_t g_skTcWsRxDataLen = 0;

// 音频流测试相关变量
bool g_skTcAudioStreamStarted = false;
bool g_skTcAudioStreamEnded = false;
bool g_skTcAudioStreamError = false;
uint32_t g_skTcAudioFrameCount = 0;

void SkTcStubWifiEvent(uint32_t event) {
    if (event == SK_WIFI_EVENT_STA_CONNECTED) {
        g_skTcWifiState = true;
    }
}

void SkTcStubOnWsEvent(void *arg, uint32_t event) {
    SK_LOGI("TC", "Websocket event %u", event);
    switch (event) {
        case SK_WS_EVENT_CONNECTED:
            if (g_skTcWsConnected) {
                g_skTcWsReconnected = true;
            }
            g_skTcWsConnected = true;
            break;
        case SK_WS_EVENT_DISCONNECTED:
            if (g_skTcWsConnected) {
                g_skTcWsDisconnect = true;
            }
            break;
        default:
            break;
    }

    return;
}

void SkTcStubWsBinDataCallback(void *arg, void *data, uint16_t len) {
    SK_LOGD("TC", "Websocket binary data recv len %u", len);

    // 首先尝试作为音频流数据处理
    if (SkAudioStreamProcessData(data, len) == SK_RET_SUCCESS) {
        g_skTcAudioFrameCount++;
        return;
    }

    // 如果不是音频流数据，按原来的方式处理
    memcpy(g_wsTsRxBuf, data, len);
    g_skTcWsRxDataLen = len;
    g_skTcWsBinDataRecv = true;
}

void SkTcStubWsTxtDataCallback(void *arg, void *data, uint16_t len) {
    memcpy(g_wsTsRxBuf, data, len);
    g_skTcWsRxDataLen = len;
    g_skTcWsTxtDataRecv = true;
    g_wsTsRxBuf[len] = '\0';
}

void SkTcStubAudioStreamEventCallback(uint8_t event, uint32_t param) {
    SK_LOGI("TC", "Audio stream event: %d, param: %u", event, param);
    switch (event) {
        case SK_AUDIO_STREAM_EVENT_START:
            g_skTcAudioStreamStarted = true;
            SK_LOGI("TC", "Audio stream started");
            break;
        case SK_AUDIO_STREAM_EVENT_END:
            g_skTcAudioStreamEnded = true;
            SK_LOGI("TC", "Audio stream ended");
            break;
        case SK_AUDIO_STREAM_EVENT_ERROR:
            g_skTcAudioStreamError = true;
            SK_LOGE("TC", "Audio stream error: %u", param);
            break;
        default:
            break;
    }
}

bool SkTcWsConnect() {
    g_skTcWsDisconnect = false;
    SkWsStopConnect();
    if (!SkTcWaitConditionSec(&g_skTcWsDisconnect, 10)) {
        SK_LOGI("TC", "Websocket disconnect timeout");
        return false;
    }
    g_skTcWsConnected = false;
    SkWsStartConnect();
    if (!SkTcWaitConditionSec(&g_skTcWsConnected, 60)) {
        SK_LOGI("TC", "Websocket connect timeout");
        return false;
    }
    g_skTcWsDisconnect = false;

    return true;
}

bool SkTcWsSendSubCase(size_t len, uint8_t flag) {
    uint8_t *outputBuf = g_wsTsTxBuf;
    uint8_t *inputBuf = g_wsTsSrcBuf;
    size_t outLen ;
    uint16_t randData = rand() % 256;
    bool binary = ((flag & SK_WS_PACKET_FLAG_BINARY) != 0);

    if (len > SK_WS_TS_BUF_SIZE) {
        SK_LOGI("TC", "Input data length error");
        return false;
    }

    for (int i = 0; i < len; i++) {
        inputBuf[i] = ((randData + i) % 26) + 'a';
    }

    outLen = SkWsPacketData(outputBuf, SK_WS_TS_BUF_SIZE, inputBuf, len, flag);
    if (outLen == 0) {
        SK_LOGI("TC", "Packet data failed");
        return false;
    }
    if (SkWsSendRaw(outputBuf, outLen) != SK_RET_SUCCESS) {
        SK_LOGI("TC", "Send data failed");
        return false;
    }
    if (binary) {
        if (!SkTcWaitConditionSec(&g_skTcWsBinDataRecv, 60)) {
            SK_LOGI("TC", "Websocket binary data recv timeout");
            return false;
        }
        g_skTcWsBinDataRecv = false;
    } else {
        if (!SkTcWaitConditionSec(&g_skTcWsTxtDataRecv, 60)) {
            SK_LOGI("TC", "Websocket binary data recv timeout");
            return false;
        }
        g_skTcWsTxtDataRecv = false;
    }

    if (g_skTcWsRxDataLen != len) {
        SK_LOGI("TC", "Binary data recv length error");
        return false;
    }
    if (memcmp(g_wsTsRxBuf, g_wsTsSrcBuf, g_skTcWsRxDataLen) != 0) {
        SK_LOGI("TC", "Binary data recv error");
        return false;
    }

    return true;
}

bool SkTcWsSendBinary() {
    uint8_t flag = SK_WS_PACKET_FLAG_BINARY;
    if (!SkWsIsConnected()) {
        SK_LOGI("TC", "Websocket is not connected");
        return false;
    }
    SK_LOGI("TC", "Subcase1: Send binary data 1 byte");
    if (!SkTcWsSendSubCase(1, flag)) {
        SK_LOGI("TC", "Send binary data 1 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 1 success");
    if (!SkTcWsSendSubCase(10, flag)) {
        SK_LOGI("TC", "Send binary data 10 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 10 success");
    if (!SkTcWsSendSubCase(100, flag)) {
        SK_LOGI("TC", "Send binary data 100 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 100 success");
    if (!SkTcWsSendSubCase(125, flag)) {
        SK_LOGI("TC", "Send binary data 125 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 125 success");
    if (!SkTcWsSendSubCase(126, flag)) {
        SK_LOGI("TC", "Send binary data 126 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 126 success");
    if (!SkTcWsSendSubCase(127, flag)) {
        SK_LOGI("TC", "Send binary data 127 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 127 success");
    if (!SkTcWsSendSubCase(512, flag)) {
        SK_LOGI("TC", "Send binary data 512 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 512 success");
    if (!SkTcWsSendSubCase(1024, flag)) {
        SK_LOGI("TC", "Send binary data 1024 failed");
        return false;
    }
    SK_LOGI("TC", "Send binary data 1024 success");
    return true;
}

bool SkTcWsSendBinaryAbnormal() {
    size_t outLen;
    uint8_t flag = SK_WS_PACKET_FLAG_BINARY;

    if (!SkWsIsConnected()) {
        SK_LOGI("TC", "Websocket is not connected");
        return false;
    }
    outLen = SkWsPacketData(g_wsTsTxBuf, SK_WS_TS_BUF_SIZE, g_wsTsRxBuf, 1280, flag);
    if (outLen == 0) {
        SK_LOGI("TC", "Packet data failed");
        return false;
    }
    g_skTcWsDisconnect = false;
    if (SkWsSendRaw(g_wsTsTxBuf, outLen) != SK_RET_SUCCESS) {
        SK_LOGI("TC", "Send data failed");
        return false;
    }
    if (!SkTcWaitConditionSec(&g_skTcWsDisconnect, 60)) {
        SK_LOGI("TC", "Websocket disconnect timeout");
        return false;
    }
    if (!SkTcWaitConditionSec(&g_skTcWsReconnected, 60)) {
        SK_LOGI("TC", "Websocket reconnect timeout");
        return false;
    }
    SK_LOGI("TC", "Send/Recv binary data 1280 success");
    return true;
}

bool SkTcWsSendText() {
    uint8_t flag = 0;

    if (!SkWsIsConnected()) {
        SK_LOGI("TC", "Websocket is not connected");
        return false;
    }
    SK_LOGI("TC", "Subcase1: Send txt data 1 byte");
    if (!SkTcWsSendSubCase(1, flag)) {
        SK_LOGI("TC", "Send txt data 1 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 1 success");
    if (!SkTcWsSendSubCase(10, flag)) {
        SK_LOGI("TC", "Send txt data 10 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 10 success");
    if (!SkTcWsSendSubCase(100, flag)) {
        SK_LOGI("TC", "Send txt data 100 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 100 success");
    if (!SkTcWsSendSubCase(125, flag)) {
        SK_LOGI("TC", "Send txt data 125 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 125 success");
    if (!SkTcWsSendSubCase(126, flag)) {
        SK_LOGI("TC", "Send txt data 126 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 126 success");
    if (!SkTcWsSendSubCase(127, flag)) {
        SK_LOGI("TC", "Send txt data 127 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 127 success");
    if (!SkTcWsSendSubCase(512, flag)) {
        SK_LOGI("TC", "Send txt data 512 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 512 success");
    if (!SkTcWsSendSubCase(1024, flag)) {
        SK_LOGI("TC", "Send txt data 1024 failed");
        return false;
    }
    SK_LOGI("TC", "Send txt data 1024 success");
    return true;
}

// 函数声明
bool SkTcWsConnect();
bool SkTcWsSendBinary();
bool SkTcWsSendText();
bool SkTcWsSendBinaryAbnormal();
bool SkTcWsAudioStreamTest();

typedef bool (*SkTcFunc)(void);
SkTcFunc SkTcFuncList[] = {
    SkTcWsAudioStreamTest,
    SkTcWsConnect,
    SkTcWsSendBinary,
    SkTcWsSendText,
    SkTcWsSendBinaryAbnormal,
};

bool SkTsWsInit() {
    SkWsInit();
    SkWsStart();
    SkWsSetServerIp("************", 8766);
    SkWsRegOnEventCallback(SkTcStubOnWsEvent, NULL);
    SkWsRegOnBinDataCallback(SkTcStubWsBinDataCallback, NULL);
    SkWsRegOnTxtDataCallback(SkTcStubWsTxtDataCallback, NULL);

    // 初始化音频流模块
    if (SkAudioStreamInit() != SK_RET_SUCCESS) {
        SK_LOGI("TS", "Audio stream init failed");
        return false;
    }
    SkAudioStreamRegEventCallback(SkTcStubAudioStreamEventCallback);

    g_wsTsTxBuf = malloc(SK_WS_TS_BUF_SIZE);
    g_wsTsRxBuf = malloc(SK_WS_TS_BUF_SIZE);
    g_wsTsSrcBuf = malloc(SK_WS_TS_BUF_SIZE);
    if (g_wsTsTxBuf == NULL || g_wsTsRxBuf == NULL || g_wsTsSrcBuf == NULL) {
        SK_LOGI("TS", "Malloc failed");
        return false;
    }

    return true;
}

bool SkTsWsDeinit() {
    SkWsStop();

    // 反初始化音频流模块
    SkAudioStreamDeinit();

    if (g_wsTsTxBuf != NULL) {
        free(g_wsTsTxBuf);
        g_wsTsTxBuf = NULL;
    }
    if (g_wsTsRxBuf != NULL) {
        free(g_wsTsRxBuf);
        g_wsTsRxBuf = NULL;
    }
    if (g_wsTsSrcBuf != NULL) {
        free(g_wsTsSrcBuf);
        g_wsTsSrcBuf = NULL;
    }
    vTaskDelay(pdMS_TO_TICKS(100));
    SkWsDeinit();
    return true;
}

bool SkTcWsInit() {
    // 重置测试状态
    g_skTcWsBinDataRecv = false;
    g_skTcWsTxtDataRecv = false;
    g_skTcWsRxDataLen = 0;

    // 检查当前连接状态
    if (SkWsIsConnected() && g_skTcWsConnected) {
        SK_LOGI("TC", "Websocket already connected");
        return true;
    }

    // 强制断开现有连接，确保状态清洁
    SK_LOGI("TC", "Ensuring clean connection state...");
    SkWsStopConnect();
    vTaskDelay(pdMS_TO_TICKS(1000)); // 等待1秒确保断开

    // 重置连接状态
    g_skTcWsConnected = false;
    g_skTcWsReconnected = false;
    g_skTcWsDisconnect = false;

    // 启动连接
    SK_LOGI("TC", "Starting websocket connection...");
    SkWsStartConnect();
    if (!SkTcWaitConditionSec(&g_skTcWsConnected, 60)) {
        SK_LOGI("TC", "Websocket connect timeout");
        return false;
    }
    SK_LOGI("TC", "Websocket connected successfully");
    return true;
}

bool SkTcWsDeinit() {
    SkWsStopConnect();
    if (!SkTcWaitConditionSec(&g_skTcWsDisconnect, 10)) {
        SK_LOGI("TC", "Websocket disconnect timeout");
        return false;
    }
    return true;
}

bool SkTcWsAudioStreamTest() {
    uint8_t playCmd[8];
    SkAudioStreamStats_t stats;

    SK_LOGI("TC", "Starting audio stream test");

    // 确保WebSocket连接
    if (!SkWsIsConnected()) {
        SK_LOGI("TC", "Websocket not connected, attempting to connect...");
        if (!SkTcWsConnect()) {
            SK_LOGI("TC", "Failed to connect websocket");
            return false;
        }
    }

    // 重置音频流测试状态
    g_skTcAudioStreamStarted = false;
    g_skTcAudioStreamEnded = false;
    g_skTcAudioStreamError = false;
    g_skTcAudioFrameCount = 0;

    // 发送音频播放请求命令
    // 格式: [CMD_TYPE][AUDIO_FORMAT][SESSION_ID_HIGH][SESSION_ID_LOW][DURATION_HIGH][DURATION_LOW][RESERVED][RESERVED]
    playCmd[0] = 0x10;  // 音频播放命令
    playCmd[1] = SK_AUDIO_FORMAT_PCM;  // PCM格式 (服务器当前支持PCM)
    playCmd[2] = 0x00;  // Session ID高字节
    playCmd[3] = 0x01;  // Session ID低字节
    playCmd[4] = 0x00;  // 播放时长高字节 (0表示由服务器控制)
    playCmd[5] = 0x00;  // 播放时长低字节
    playCmd[6] = 0x00;  // 保留字节
    playCmd[7] = 0x00;  // 保留字节
    
    SK_LOGI("TC", "Sending audio play command");
    if (SkWsSendRaw(playCmd, sizeof(playCmd)) != SK_RET_SUCCESS) {
        SK_LOGI("TC", "Failed to send audio play command");
        return false;
    }

    // 等待音频流开始
    if (!SkTcWaitConditionSec(&g_skTcAudioStreamStarted, 30)) {
        SK_LOGI("TC", "Audio stream start timeout");
        return false;
    }

    SK_LOGI("TC", "Audio stream started, waiting for frames...");

    // 等待接收音频帧 (最多等待60秒)
    uint32_t waitTime = 0;
    uint32_t lastFrameCount = 0;
    while (waitTime < 60) {
        vTaskDelay(pdMS_TO_TICKS(1000));
        waitTime++;

        if (g_skTcAudioFrameCount > lastFrameCount) {
            lastFrameCount = g_skTcAudioFrameCount;
            SK_LOGI("TC", "Received %u audio frames", g_skTcAudioFrameCount);
        }

        // 检查是否有错误
        if (g_skTcAudioStreamError) {
            SK_LOGI("TC", "Audio stream error occurred");
            return false;
        }

        // 检查是否结束
        if (g_skTcAudioStreamEnded) {
            SK_LOGI("TC", "Audio stream ended normally");
            break;
        }

        // 如果超过10秒没有新帧，认为测试完成
        if (g_skTcAudioFrameCount > 0 && waitTime > 10) {
            SK_LOGI("TC", "No new frames for 10 seconds, test completed");
            break;
        }
    }

    // 获取统计信息
    if (SkAudioStreamGetStats(&stats) == SK_RET_SUCCESS) {
        SK_LOGI("TC", "Audio stream stats:");
        SK_LOGI("TC", "  Total frames: %u", stats.totalFrames);
        SK_LOGI("TC", "  Lost frames: %u", stats.lostFrames);
        SK_LOGI("TC", "  Duplicate frames: %u", stats.duplicateFrames);
        SK_LOGI("TC", "  Total bytes: %u", stats.totalBytes);
        SK_LOGI("TC", "  Play time: %u ms", stats.playTime);
    }

    // 验证测试结果
    if (g_skTcAudioFrameCount == 0) {
        SK_LOGI("TC", "No audio frames received");
        return false;
    }

    if (g_skTcAudioStreamError) {
        SK_LOGI("TC", "Audio stream had errors");
        return false;
    }

    SK_LOGI("TC", "Audio stream test completed successfully");
    SK_LOGI("TC", "Received %u audio frames", g_skTcAudioFrameCount);

    return true;
}

void SkTestWebSocketMain() {
    uint32_t i = 0;

    // 初始化硬件板级支持 (包括音频硬件)
    SK_LOGI("TC", "Initializing board hardware...");
    ESP_ERROR_CHECK(SkBspBoardInit(16000, sizeof(uint16_t) * 8));
    SK_LOGI("TC", "Board hardware initialized successfully");

    SkConfigInit();
    SkWifiInit();
    SkWifiRegEventCb(SkTcStubWifiEvent);
    SkWifiStartSta();

    if (!SkTcWaitConditionSec(&g_skTcWifiState, 60)) {
        SK_LOGI("TC", "Wifi connect timeout");
        return;
    }

    // 初始化音频系统 (WebSocket音频流测试需要)
    SK_LOGI("TC", "Initializing audio system for WebSocket test...");
    SkOpusInit(16000, 1, 60);
    SkAudioInit(sizeof(uint16_t), 960);
    SkPlayerSetCallback(SkOpusDecFeedPlayAudio);
    SK_LOGI("TC", "Audio system initialized successfully");

    // 播放初始化音频测试 (1, 2, 3, 4)
    SK_LOGI("TC", "Playing initialization audio test...");
    uint8_t audioList[4] = {AUDIO_IDX_1, AUDIO_IDX_2, AUDIO_IDX_3, AUDIO_IDX_4};
    uint32_t workId = SkOpusDecPlayLocal(audioList, 4);
    SK_LOGI("TC", "Started audio playback, work ID: %u", workId);

    // 等待音频播放完成 (大约4秒)
    vTaskDelay(pdMS_TO_TICKS(5000));
    SK_LOGI("TC", "Audio initialization test completed");

    if (!SkTsWsInit()) {
        SK_LOGI("TC", "Test suit init failed");
        return;
    }
    for (i = 0; i < sizeof(SkTcFuncList) / sizeof(SkTcFuncList[0]); i++) {
        SK_LOGI("TC", "Test case %d init...", i);
        if (!SkTcWsInit()) {
            SK_LOGI("TC", "Test case %d init failed", i);
            return;
        }
        bool result = SkTcFuncList[i]();
        if (!result) {
            SK_LOGI("TC", "Test case %d failed", i);
            return;
        } else {
            SK_LOGI("TC", "Test case %d passed", i);
        }
        SK_LOGI("TC", "Test case %d deinit...", i);
        if (!SkTcWsDeinit()) {
            SK_LOGI("TC", "Test case %d deinit failed", i);
            return;
        }
    }
    SkTsWsDeinit();
}