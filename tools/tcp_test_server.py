#!/usr/bin/env python3
"""
简单的TCP测试服务器，用于调试网络连接问题
"""

import socket
import threading
import datetime

def handle_client(client_socket, client_address):
    """处理客户端连接"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] 客户端连接: {client_address[0]}:{client_address[1]}")
    
    try:
        while True:
            # 接收数据
            data = client_socket.recv(1024)
            if not data:
                break
                
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{timestamp}] 收到数据 ({len(data)} 字节): {data}")
            
            # 回显数据
            client_socket.send(data)
            print(f"[{timestamp}] 已回显数据")
            
    except Exception as e:
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] 客户端处理异常: {e}")
    finally:
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] 客户端 {client_address[0]}:{client_address[1]} 断开连接")
        client_socket.close()

def main():
    # 创建TCP服务器
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    
    try:
        # 绑定地址和端口 - 使用0.0.0.0监听所有接口
        server_socket.bind(('************', 8765))
        server_socket.listen(5)

        # 获取本机实际IP地址
        import subprocess
        try:
            result = subprocess.run(['hostname', '-I'], capture_output=True, text=True)
            local_ips = result.stdout.strip().split()
            main_ip = local_ips[0] if local_ips else "unknown"
        except:
            main_ip = "unknown"

        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] TCP测试服务器启动")
        print(f"[{timestamp}] 监听地址: ************:8765 (所有接口)")
        print(f"[{timestamp}] 本机IP地址: {main_ip}")
        print(f"[{timestamp}] ESP32应连接到: {main_ip}:8765")
        print(f"[{timestamp}] 等待客户端连接...")
        print(f"[{timestamp}] 测试命令: telnet {main_ip} 8765")
        
        while True:
            # 接受客户端连接
            client_socket, client_address = server_socket.accept()
            
            # 为每个客户端创建新线程
            client_thread = threading.Thread(
                target=handle_client,
                args=(client_socket, client_address)
            )
            client_thread.daemon = True
            client_thread.start()
            
    except OSError as e:
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] 服务器启动失败: {e}")
        if e.errno == 98:  # Address already in use
            print(f"[{timestamp}] 端口8765已被占用，请检查:")
            print(f"[{timestamp}]   netstat -an | grep 8765")
            print(f"[{timestamp}]   lsof -i :8765")
        elif e.errno == 13:  # Permission denied
            print(f"[{timestamp}] 权限不足，请使用sudo运行")
    except KeyboardInterrupt:
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"\n[{timestamp}] 服务器已停止")
    finally:
        server_socket.close()

if __name__ == "__main__":
    main()
