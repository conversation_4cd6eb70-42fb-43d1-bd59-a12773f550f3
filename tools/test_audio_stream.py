#!/usr/bin/env python3
"""
简化的WebSocket音频流测试服务器
用于快速测试ESP32终端的音频流播放功能

使用方法:
1. 运行服务器: python test_audio_stream.py
2. 终端连接后会自动开始音频流测试
3. 观察终端日志输出

作者: <PERSON>
日期: 2025-07-28
"""

import asyncio
import websockets
import struct
import logging
import math

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 服务器配置
SERVER_HOST = "0.0.0.0"
SERVER_PORT = 8765

# 音频流协议常量
SK_AUDIO_STREAM_VERSION = 0x01
SK_WS_DATA_TYPE_AUDIO = 0x01
SK_AUDIO_FORMAT_PCM = 0x01

# 音频流标志位
SK_AUDIO_FLAG_START = 0x01
SK_AUDIO_FLAG_END = 0x02

# 音频参数
SAMPLE_RATE = 16000
FRAME_DURATION_MS = 60
FRAME_SIZE = int(SAMPLE_RATE * FRAME_DURATION_MS / 1000)  # 960样本

def create_audio_frame(seq_num: int, audio_data: bytes, flags: int = 0) -> bytes:
    """创建音频帧"""
    payload_len = len(audio_data)
    
    # 构造音频帧头部
    frame_header = struct.pack('<BBHHBB',
        SK_AUDIO_STREAM_VERSION,    # version
        SK_WS_DATA_TYPE_AUDIO,      # type
        seq_num,                    # seqNum
        payload_len,                # payloadLen
        SK_AUDIO_FORMAT_PCM,        # audioFormat
        flags                       # flags
    )
    
    return frame_header + audio_data

def generate_test_audio(duration_seconds: int = 5) -> bytes:
    """生成测试音频数据 (440Hz正弦波)"""
    total_samples = SAMPLE_RATE * duration_seconds
    audio_data = bytearray()
    
    for i in range(total_samples):
        # 生成440Hz正弦波
        sample = int(32767 * 0.3 * math.sin(2 * math.pi * 440 * i / SAMPLE_RATE))
        # 转换为16位小端格式
        audio_data.extend(struct.pack('<h', sample))
    
    return bytes(audio_data)

async def send_audio_stream(websocket, session_id: int = 1):
    """发送音频流"""
    logger.info(f"Starting audio stream for session {session_id}")
    
    # 生成5秒的测试音频
    test_audio = generate_test_audio(5)
    total_frames = len(test_audio) // (FRAME_SIZE * 2)  # 2 bytes per sample
    
    logger.info(f"Generated {len(test_audio)} bytes of audio data ({total_frames} frames)")
    
    seq_num = 0
    
    try:
        # 发送开始帧
        start_frame = create_audio_frame(seq_num, b'', SK_AUDIO_FLAG_START)
        await websocket.send(start_frame)
        logger.info(f"Sent start frame (seq: {seq_num})")
        seq_num += 1
        
        # 发送音频数据帧
        for frame_idx in range(total_frames):
            start_pos = frame_idx * FRAME_SIZE * 2
            end_pos = min(start_pos + FRAME_SIZE * 2, len(test_audio))
            frame_data = test_audio[start_pos:end_pos]
            
            # 如果最后一帧数据不足，用零填充
            if len(frame_data) < FRAME_SIZE * 2:
                frame_data += b'\x00' * (FRAME_SIZE * 2 - len(frame_data))
            
            # 创建并发送音频帧
            frame = create_audio_frame(seq_num, frame_data)
            await websocket.send(frame)
            
            if frame_idx % 10 == 0:  # 每10帧打印一次日志
                logger.info(f"Sent frame {seq_num}/{total_frames}")
            
            seq_num += 1
            
            # 控制发送速率 (60ms间隔)
            await asyncio.sleep(FRAME_DURATION_MS / 1000.0)
        
        # 发送结束帧
        end_frame = create_audio_frame(seq_num, b'', SK_AUDIO_FLAG_END)
        await websocket.send(end_frame)
        logger.info(f"Sent end frame (seq: {seq_num})")
        logger.info(f"Audio stream completed. Total frames: {total_frames}")
        
    except Exception as e:
        logger.error(f"Error sending audio stream: {e}")

async def handle_client_message(websocket, message):
    """处理客户端消息"""
    if isinstance(message, bytes) and len(message) >= 8:
        # 解析播放命令
        cmd_type = message[0]
        if cmd_type == 0x10:  # 音频播放命令
            audio_format = message[1]
            session_id = (message[2] << 8) | message[3]
            duration = (message[4] << 8) | message[5]
            
            logger.info(f"Received play command: format={audio_format}, session={session_id}, duration={duration}")
            
            # 开始发送音频流
            await send_audio_stream(websocket, session_id)
        else:
            logger.warning(f"Unknown command type: 0x{cmd_type:02x}")
    else:
        logger.info(f"Received text message: {message}")

async def handle_client(websocket, path):
    """处理客户端连接"""
    client_addr = websocket.remote_address
    logger.info(f"Client {client_addr} connected")
    
    try:
        async for message in websocket:
            await handle_client_message(websocket, message)
    except websockets.exceptions.ConnectionClosed:
        logger.info(f"Client {client_addr} disconnected")
    except Exception as e:
        logger.error(f"Error handling client {client_addr}: {e}")

async def main():
    """主函数"""
    logger.info(f"Starting WebSocket audio test server on {SERVER_HOST}:{SERVER_PORT}")
    logger.info("Waiting for ESP32 client connections...")
    
    server = await websockets.serve(handle_client, SERVER_HOST, SERVER_PORT)
    logger.info("Server started successfully")
    
    await server.wait_closed()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
