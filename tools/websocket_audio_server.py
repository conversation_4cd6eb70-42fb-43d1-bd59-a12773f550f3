#!/usr/bin/env python3
"""
WebSocket音频流服务器
支持向ESP32终端发送Opus音频流进行实时播放

使用方法:
1. 安装依赖: pip install websockets pydub
2. 准备音频文件: 将音频文件放在同目录下，命名为 test_audio.wav
3. 运行服务器: python websocket_audio_server.py
4. 终端连接到 ws://服务器IP:8765

作者: <PERSON>
日期: 2025-07-28
"""

import asyncio
import websockets
import wave
import struct
import os
import logging
from typing import Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 服务器配置
SERVER_HOST = "0.0.0.0"
SERVER_PORT = 8765

# 音频流协议常量
SK_AUDIO_STREAM_VERSION = 0x01
SK_WS_DATA_TYPE_AUDIO = 0x01
SK_AUDIO_FORMAT_PCM = 0x01
SK_AUDIO_FORMAT_OPUS = 0x02

# 音频流标志位
SK_AUDIO_FLAG_START = 0x01
SK_AUDIO_FLAG_END = 0x02
SK_AUDIO_FLAG_MUTE = 0x04
SK_AUDIO_FLAG_SYNC = 0x08

# 音频参数
SAMPLE_RATE = 16000
CHANNELS = 1
FRAME_DURATION_MS = 60  # 每帧60ms
FRAME_SIZE = int(SAMPLE_RATE * FRAME_DURATION_MS / 1000)  # 960样本

class AudioStreamServer:
    def __init__(self):
        self.clients = set()
        self.audio_file = "test_audio.wav"
        
    async def register_client(self, websocket):
        """注册新客户端"""
        self.clients.add(websocket)
        logger.info(f"Client {websocket.remote_address} connected. Total clients: {len(self.clients)}")
        
    async def unregister_client(self, websocket):
        """注销客户端"""
        self.clients.discard(websocket)
        logger.info(f"Client {websocket.remote_address} disconnected. Total clients: {len(self.clients)}")
        
    def create_audio_frame(self, seq_num: int, audio_data: bytes, flags: int = 0) -> bytes:
        """创建音频帧"""
        payload_len = len(audio_data)
        
        # 构造音频帧头部
        frame_header = struct.pack('<BBHHBB',
            SK_AUDIO_STREAM_VERSION,    # version
            SK_WS_DATA_TYPE_AUDIO,      # type
            seq_num,                    # seqNum
            payload_len,                # payloadLen
            SK_AUDIO_FORMAT_PCM,        # audioFormat (使用PCM简化实现)
            flags                       # flags
        )
        
        return frame_header + audio_data
        
    async def send_audio_stream(self, websocket, session_id: int = 1):
        """发送音频流"""
        if not os.path.exists(self.audio_file):
            logger.error(f"Audio file {self.audio_file} not found")
            return
            
        try:
            with wave.open(self.audio_file, 'rb') as wf:
                # 检查音频格式
                if wf.getframerate() != SAMPLE_RATE:
                    logger.warning(f"Audio sample rate {wf.getframerate()} != {SAMPLE_RATE}")
                if wf.getnchannels() != CHANNELS:
                    logger.warning(f"Audio channels {wf.getnchannels()} != {CHANNELS}")
                if wf.getsampwidth() != 2:
                    logger.warning(f"Audio sample width {wf.getsampwidth()} != 2")
                    
                logger.info(f"Starting audio stream for session {session_id}")
                logger.info(f"Audio info: {wf.getframerate()}Hz, {wf.getnchannels()}ch, {wf.getsampwidth()}bytes")
                
                seq_num = 0
                total_frames = 0
                
                # 发送开始帧
                start_frame = self.create_audio_frame(seq_num, b'', SK_AUDIO_FLAG_START)
                await websocket.send(start_frame)
                logger.info(f"Sent start frame (seq: {seq_num})")
                seq_num += 1
                
                # 发送音频数据帧
                while True:
                    # 读取一帧音频数据 (60ms)
                    audio_data = wf.readframes(FRAME_SIZE)
                    if not audio_data:
                        break
                        
                    # 如果数据不足一帧，用零填充
                    if len(audio_data) < FRAME_SIZE * 2:  # 2 bytes per sample
                        padding = b'\x00' * (FRAME_SIZE * 2 - len(audio_data))
                        audio_data += padding
                        
                    # 创建并发送音频帧
                    frame = self.create_audio_frame(seq_num, audio_data)
                    await websocket.send(frame)
                    
                    total_frames += 1
                    if total_frames % 10 == 0:  # 每10帧打印一次日志
                        logger.info(f"Sent frame {seq_num}, total: {total_frames}")
                        
                    seq_num += 1
                    
                    # 控制发送速率 (60ms间隔)
                    await asyncio.sleep(FRAME_DURATION_MS / 1000.0)
                    
                # 发送结束帧
                end_frame = self.create_audio_frame(seq_num, b'', SK_AUDIO_FLAG_END)
                await websocket.send(end_frame)
                logger.info(f"Sent end frame (seq: {seq_num})")
                logger.info(f"Audio stream completed. Total frames: {total_frames}")
                
        except Exception as e:
            logger.error(f"Error sending audio stream: {e}")
            
    async def handle_client_message(self, websocket, message):
        """处理客户端消息"""
        if isinstance(message, bytes) and len(message) >= 8:
            # 解析播放命令
            cmd_type = message[0]
            if cmd_type == 0x10:  # 音频播放命令
                audio_format = message[1]
                session_id = (message[2] << 8) | message[3]
                duration = (message[4] << 8) | message[5]
                
                logger.info(f"Received play command: format={audio_format}, session={session_id}, duration={duration}")
                
                # 开始发送音频流
                await self.send_audio_stream(websocket, session_id)
            else:
                logger.warning(f"Unknown command type: 0x{cmd_type:02x}")
        else:
            logger.info(f"Received text message: {message}")
            
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        await self.register_client(websocket)
        try:
            async for message in websocket:
                await self.handle_client_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            logger.info("Client connection closed")
        except Exception as e:
            logger.error(f"Error handling client: {e}")
        finally:
            await self.unregister_client(websocket)
            
    async def start_server(self):
        """启动服务器"""
        logger.info(f"Starting WebSocket audio server on {SERVER_HOST}:{SERVER_PORT}")
        
        # 检查音频文件
        if not os.path.exists(self.audio_file):
            logger.warning(f"Audio file {self.audio_file} not found. Please provide a 16kHz mono WAV file.")
            
        server = await websockets.serve(self.handle_client, SERVER_HOST, SERVER_PORT)
        logger.info("WebSocket audio server started successfully")
        logger.info("Waiting for client connections...")
        
        await server.wait_closed()

def create_test_audio():
    """创建测试音频文件"""
    import numpy as np
    
    duration = 10  # 10秒
    sample_rate = 16000
    frequency = 440  # A4音符
    
    # 生成正弦波
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    wave_data = np.sin(2 * np.pi * frequency * t) * 0.3
    
    # 转换为16位整数
    wave_data = (wave_data * 32767).astype(np.int16)
    
    # 保存为WAV文件
    with wave.open("test_audio.wav", 'wb') as wf:
        wf.setnchannels(1)  # 单声道
        wf.setsampwidth(2)  # 16位
        wf.setframerate(16000)  # 16kHz
        wf.writeframes(wave_data.tobytes())
        
    logger.info("Created test audio file: test_audio.wav")

async def main():
    """主函数"""
    # 如果没有测试音频文件，创建一个
    if not os.path.exists("test_audio.wav"):
        try:
            create_test_audio()
        except ImportError:
            logger.error("numpy not installed. Please install it or provide test_audio.wav manually")
            logger.error("pip install numpy")
            return
            
    # 启动服务器
    server = AudioStreamServer()
    await server.start_server()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
