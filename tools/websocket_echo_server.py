#!/usr/bin/env python3
"""
WebSocket回显服务器
用于测试ESP32终端的WebSocket基础功能

功能:
1. 接收客户端发送的文本和二进制数据
2. 原样回显给客户端
3. 支持音频流播放命令

使用方法:
python websocket_echo_server.py

作者: <PERSON>
日期: 2025-07-28
"""

import asyncio
import websockets
import struct
import logging
import math

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 服务器配置
SERVER_HOST = "0.0.0.0"
SERVER_PORT = 8766

# 音频流协议常量
SK_AUDIO_STREAM_VERSION = 0x01
SK_WS_DATA_TYPE_AUDIO = 0x01
SK_AUDIO_FORMAT_PCM = 0x01
SK_AUDIO_FLAG_START = 0x01
SK_AUDIO_FLAG_END = 0x02

# 音频参数
SAMPLE_RATE = 16000
FRAME_DURATION_MS = 60
FRAME_SIZE = int(SAMPLE_RATE * FRAME_DURATION_MS / 1000)  # 960样本

class WebSocketEchoServer:
    def __init__(self):
        self.clients = set()
        
    async def register_client(self, websocket):
        """注册新客户端"""
        self.clients.add(websocket)
        logger.info(f"Client {websocket.remote_address} connected. Total clients: {len(self.clients)}")
        
    async def unregister_client(self, websocket):
        """注销客户端"""
        self.clients.discard(websocket)
        logger.info(f"Client {websocket.remote_address} disconnected. Total clients: {len(self.clients)}")

    def create_audio_frame(self, seq_num: int, audio_data: bytes, flags: int = 0) -> bytes:
        """创建音频帧"""
        payload_len = len(audio_data)
        
        frame_header = struct.pack('<BBHHBB',
            SK_AUDIO_STREAM_VERSION,    # version
            SK_WS_DATA_TYPE_AUDIO,      # type
            seq_num,                    # seqNum
            payload_len,                # payloadLen
            SK_AUDIO_FORMAT_PCM,        # audioFormat
            flags                       # flags
        )
        
        return frame_header + audio_data

    def generate_test_audio_frame(self, frame_idx: int) -> bytes:
        """生成一帧测试音频数据"""
        audio_data = bytearray()
        
        for i in range(FRAME_SIZE):
            # 生成440Hz正弦波
            sample_idx = frame_idx * FRAME_SIZE + i
            sample = int(32767 * 0.3 * math.sin(2 * math.pi * 440 * sample_idx / SAMPLE_RATE))
            audio_data.extend(struct.pack('<h', sample))
        
        return bytes(audio_data)

    async def send_audio_stream(self, websocket, session_id: int = 1):
        """发送音频流"""
        logger.info(f"Starting audio stream for session {session_id}")
        
        total_frames = 83  # 约5秒的音频 (83 * 60ms = 4.98s)
        seq_num = 0
        
        try:
            # 发送开始帧
            start_frame = self.create_audio_frame(seq_num, b'', SK_AUDIO_FLAG_START)
            await websocket.send(start_frame)
            logger.info(f"Sent start frame (seq: {seq_num})")
            seq_num += 1
            
            # 发送音频数据帧
            for frame_idx in range(total_frames):
                frame_data = self.generate_test_audio_frame(frame_idx)
                frame = self.create_audio_frame(seq_num, frame_data)
                await websocket.send(frame)
                
                if frame_idx % 20 == 0:  # 每20帧打印一次日志
                    logger.info(f"Sent audio frame {seq_num}/{total_frames}")
                
                seq_num += 1
                
                # 控制发送速率 (60ms间隔)
                await asyncio.sleep(FRAME_DURATION_MS / 1000.0)
            
            # 发送结束帧
            end_frame = self.create_audio_frame(seq_num, b'', SK_AUDIO_FLAG_END)
            await websocket.send(end_frame)
            logger.info(f"Sent end frame (seq: {seq_num})")
            logger.info(f"Audio stream completed. Total frames: {total_frames}")
            
        except Exception as e:
            logger.error(f"Error sending audio stream: {e}")

    async def handle_client_message(self, websocket, message):
        """处理客户端消息"""
        if isinstance(message, bytes):
            logger.info(f"Received binary message: {len(message)} bytes")
            
            # 检查是否是音频播放命令
            if len(message) >= 8 and message[0] == 0x10:
                audio_format = message[1]
                session_id = (message[2] << 8) | message[3]
                duration = (message[4] << 8) | message[5]
                
                logger.info(f"Audio play command: format={audio_format}, session={session_id}, duration={duration}")
                
                # 开始发送音频流
                await self.send_audio_stream(websocket, session_id)
            else:
                # 回显二进制数据
                logger.info(f"Echoing binary data: {message[:20].hex()}...")
                await websocket.send(message)
        else:
            # 回显文本数据
            logger.info(f"Received text message: {message}")
            await websocket.send(message)

    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        await self.register_client(websocket)
        try:
            async for message in websocket:
                await self.handle_client_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            logger.info("Client connection closed normally")
        except Exception as e:
            logger.error(f"Error handling client: {e}")
        finally:
            await self.unregister_client(websocket)

    async def start_server(self):
        """启动服务器"""
        logger.info(f"Starting WebSocket echo server on {SERVER_HOST}:{SERVER_PORT}")
        
        server = await websockets.serve(self.handle_client, SERVER_HOST, SERVER_PORT)
        logger.info("WebSocket echo server started successfully")
        logger.info("Waiting for client connections...")
        
        await server.wait_closed()

async def main():
    """主函数"""
    server = WebSocketEchoServer()
    await server.start_server()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
