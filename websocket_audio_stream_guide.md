# WebSocket音频流播放方案实现指南

## 🎵 概述

本方案实现了通过WebSocket服务器向ESP32终端发送Opus音频流进行实时播放的功能。服务器将音频文件分片成小的音频帧，通过WebSocket实时发送给终端，终端接收后通过Opus解码器解码并播放。

## 🏗️ 架构设计

```
服务器音频文件 → 音频分帧 → WebSocket二进制帧 → 终端接收 → 音频解码 → 播放器 → 扬声器
```

### 核心组件

1. **音频流处理模块** (`sk_audio_stream.c/h`)
   - 处理WebSocket接收到的音频帧
   - 管理音频流状态和统计信息
   - 与Opus解码器集成

2. **WebSocket服务器** (`websocket_audio_server.py`)
   - 读取音频文件并分帧
   - 通过WebSocket发送音频流
   - 支持播放控制命令

3. **测试用例** (`testcase_websocket.c`)
   - 音频流播放测试
   - 统计信息验证
   - 错误处理测试

## 📊 音频帧格式

```c
typedef struct {
    uint8_t version;        // 协议版本 (0x01)
    uint8_t type;           // 数据类型 (0x01=音频)
    uint16_t seqNum;        // 序列号
    uint16_t payloadLen;    // 音频数据长度
    uint8_t audioFormat;    // 音频格式 (0x01=PCM, 0x02=Opus)
    uint8_t flags;          // 标志位
    uint8_t audioData[0];   // 音频数据
} SkWsAudioFrame_t;
```

### 标志位定义
- `0x01`: 流开始
- `0x02`: 流结束
- `0x04`: 静音帧
- `0x08`: 同步帧

## 🚀 使用方法

### 1. 终端端配置

#### 编译配置
确保在CMakeLists.txt中包含了音频流模块：
```cmake
set(srcs
    # ... 其他文件
    sk_audio_stream.c
)
```

#### 测试配置
在测试用例中设置服务器IP：
```c
SkWsSetServerIp("***********", 8765);  // 修改为实际服务器IP
```

#### 编译和运行
```bash
# 编译测试版本
./scripts/build.sh -t 1

# 烧录到设备
idf.py flash monitor
```

### 2. 服务器端配置

#### 安装依赖
```bash
pip install websockets numpy
```

#### 准备音频文件
将16kHz单声道WAV音频文件放在服务器目录下，命名为`test_audio.wav`。

如果没有合适的音频文件，服务器会自动生成一个440Hz的测试音频。

#### 启动服务器
```bash
cd tools
python websocket_audio_server.py
```

服务器将在`0.0.0.0:8765`上监听连接。

### 3. 运行测试

1. **启动服务器**
   ```bash
   python tools/websocket_audio_server.py
   ```

2. **连接终端**
   - 确保终端和服务器在同一网络
   - 终端会自动连接WiFi并运行测试

3. **观察测试结果**
   ```
   I (xxxxx) TC: Test case 4 init...
   I (xxxxx) TC: Starting audio stream test
   I (xxxxx) TC: Sending audio play command
   I (xxxxx) TC: Audio stream started
   I (xxxxx) TC: Received 10 audio frames
   I (xxxxx) TC: Audio stream test completed successfully
   I (xxxxx) TC: Test case 4 passed
   ```

## 📈 性能参数

### 音频参数
- **采样率**: 16kHz
- **位深**: 16bit
- **声道**: 单声道
- **帧大小**: 960样本 (60ms)
- **帧间隔**: 60ms

### 网络参数
- **协议**: WebSocket over TCP
- **帧头大小**: 8字节
- **PCM帧大小**: 1928字节 (8字节头 + 1920字节数据)
- **带宽需求**: ~25KB/s (PCM格式)

### 缓冲参数
- **接收缓冲**: 1280字节 (WebSocket缓冲)
- **解码缓冲**: 由Opus解码器管理
- **播放缓冲**: 2-3帧 (120-180ms)

## 🔧 API接口

### 音频流控制
```c
// 初始化音频流模块
int32_t SkAudioStreamInit(void);

// 开始音频流播放
int32_t SkAudioStreamStart(uint32_t sessionId);

// 停止音频流播放
int32_t SkAudioStreamStop(void);

// 暂停/恢复播放
int32_t SkAudioStreamPause(void);
int32_t SkAudioStreamResume(void);
```

### 状态查询
```c
// 获取播放状态
uint8_t SkAudioStreamGetState(void);

// 获取统计信息
int32_t SkAudioStreamGetStats(SkAudioStreamStats_t *stats);
```

### 事件回调
```c
// 注册事件回调
void SkAudioStreamRegEventCallback(SkAudioStreamEventCallback_t callback);
```

## 📊 统计信息

系统提供详细的播放统计信息：

```c
typedef struct {
    uint32_t totalFrames;       // 总帧数
    uint32_t lostFrames;        // 丢失帧数
    uint32_t duplicateFrames;   // 重复帧数
    uint32_t totalBytes;        // 总字节数
    uint32_t playTime;          // 播放时间(ms)
} SkAudioStreamStats_t;
```

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 确认服务器IP和端口
   - 检查防火墙设置

2. **音频不播放**
   - 检查Opus解码器初始化
   - 确认音频格式匹配
   - 查看错误日志

3. **音频断续**
   - 检查网络稳定性
   - 调整缓冲区大小
   - 优化发送间隔

### 调试日志

启用详细日志：
```c
// 在sk_audio_stream.c中设置日志级别
#define LOG_LOCAL_LEVEL ESP_LOG_DEBUG
```

### 性能监控

查看统计信息：
```c
SkAudioStreamStats_t stats;
SkAudioStreamGetStats(&stats);
SK_LOGI("STATS", "Frames: %u, Lost: %u, Bytes: %u", 
        stats.totalFrames, stats.lostFrames, stats.totalBytes);
```

## 🔮 扩展功能

### 支持Opus格式
当前实现使用PCM格式简化开发。要支持Opus格式：

1. 在服务器端添加Opus编码
2. 修改`ProcessOpusFrame`函数
3. 调整帧大小和缓冲管理

### 多客户端支持
服务器已支持多客户端连接，可以同时向多个终端发送音频流。

### 播放控制
可以扩展支持：
- 音量控制
- 播放进度控制
- 多音频文件切换

## 📝 注意事项

1. **内存使用**: 音频流会占用一定内存，注意监控内存使用情况
2. **网络稳定性**: 确保网络连接稳定，避免音频中断
3. **音频格式**: 确保音频文件格式与系统参数匹配
4. **延迟控制**: 调整缓冲区大小平衡延迟和稳定性

## 📚 相关文档

- [WebSocket协议规范](https://tools.ietf.org/html/rfc6455)
- [Opus音频编解码器](https://opus-codec.org/)
- [ESP-IDF音频开发指南](https://docs.espressif.com/projects/esp-idf/en/latest/esp32s3/api-reference/peripherals/i2s.html)
